// 从配置文件加载数据并渲染页面
async function loadConfig() {
  try {
    const response = await fetch('./config.json');
    const config = await response.json();
    renderPage(config);
  } catch (error) {
    console.error('加载配置文件失败:', error);
    // 如果配置文件不存在，使用默认数据
    renderPage(getDefaultConfig());
  }
}

// 渲染页面内容
function renderPage(config) {
  renderProfile(config.profile);
  renderBasicInfo(config.basicInfo);
  renderExternalLinks(config.externalLinks);
  renderIntroduction(config.introduction);
  renderNews(config.news);
  renderEducation(config.education);
  renderExperience(config.experience);
  renderPublications(config.publications);
}

// 渲染个人资料
function renderProfile(profile) {
  document.getElementById('avatar').src = profile.avatar || '';
  document.getElementById('name').textContent = profile.name || '';
  document.getElementById('title').textContent = profile.title || '';
  document.title = profile.name || 'Portfolio';
}

// 渲染基本信息
function renderBasicInfo(basicInfo) {
  const container = document.getElementById('basicInfoList');
  container.innerHTML = '';

  basicInfo.forEach(item => {
    const div = document.createElement('div');
    div.className = 'info-item';
    div.innerHTML = `
            <div class="info-icon">${item.icon || ''}</div>
            <span class="info-text">${item.text}</span>
        `;
    container.appendChild(div);
  });
}

// 渲染外部链接
function renderExternalLinks(externalLinks) {
  const container = document.getElementById('externalLinksList');
  container.innerHTML = '';

  externalLinks.forEach(link => {
    const div = document.createElement('div');
    div.className = 'link-item';
    div.innerHTML = `
            <div class="link-icon">${link.icon || ''}</div>
            <a href="${link.url}" target="_blank">${link.name}</a>
        `;
    container.appendChild(div);
  });
}

// 渲染个人介绍
function renderIntroduction(introduction) {
  document.getElementById('introContent').innerHTML = introduction || '';
}

// 渲染新闻
function renderNews(news) {
  const container = document.getElementById('newsList');
  container.innerHTML = '';

  news.forEach(item => {
    const div = document.createElement('div');
    div.className = 'news-item';
    div.innerHTML = `
            <div class="news-date">${item.date}</div>
            <div class="news-content">${item.content}</div>
        `;
    container.appendChild(div);
  });
}

// 渲染教育经历
function renderEducation(education) {
  const container = document.getElementById('educationList');
  container.innerHTML = '';

  education.forEach(item => {
    const div = document.createElement('div');
    div.className = 'timeline-item';
    div.innerHTML = `
            <div class="timeline-icon">
                ${item.logo ? `<img src="${item.logo}" alt="${item.institution}">` : ''}
            </div>
            <div class="timeline-content">
                <div class="timeline-title">${item.degree}</div>
                <div class="timeline-org">${item.institution}</div>
                <div class="timeline-date">${item.period}</div>
                ${item.description ? `<div class="timeline-desc">${item.description}</div>` : ''}
            </div>
        `;
    container.appendChild(div);
  });
}

// 渲染工作经历
function renderExperience(experience) {
  const container = document.getElementById('experienceList');
  container.innerHTML = '';

  experience.forEach(item => {
    const div = document.createElement('div');
    div.className = 'timeline-item';
    div.innerHTML = `
            <div class="timeline-icon">
                ${item.logo ? `<img src="${item.logo}" alt="${item.company}">` : ''}
            </div>
            <div class="timeline-content">
                <div class="timeline-title">${item.position}</div>
                <div class="timeline-org">${item.company}</div>
                <div class="timeline-date">${item.period}</div>
                ${item.description ? `<div class="timeline-desc">${item.description}</div>` : ''}
            </div>
        `;
    container.appendChild(div);
  });
}

// 渲染出版物
function renderPublications(publications) {
  const container = document.getElementById('publicationsList');
  container.innerHTML = '';

  publications.forEach(item => {
    const div = document.createElement('div');
    div.className = 'publication-item';
    div.innerHTML = `
            <div class="publication-title">
                ${item.url ? `<a href="${item.url}" target="_blank">${item.title}</a>` : item.title}
            </div>
            <div class="publication-authors">${item.authors}</div>
            <div class="publication-venue">${item.venue}</div>
            ${item.description ? `<div class="publication-desc">${item.description}</div>` : ''}
        `;
    container.appendChild(div);
  });
}

// 默认配置数据
function getDefaultConfig() {
  return {
    profile: {
      name: "Your Name",
      title: "Your Title",
      avatar: "https://via.placeholder.com/150"
    },
    basicInfo: [
      { icon: "", text: "Beijing, China" },
      { icon: "", text: "<EMAIL>" }
    ],
    externalLinks: [
      { name: "GitHub", url: "#", icon: "" },
      { name: "Google Scholar", url: "#", icon: "" },
      { name: "LinkedIn", url: "#", icon: "" }
    ],
    introduction: "请在config.json中添加您的个人介绍...",
    news: [
      { date: "2024.01", content: "示例新闻内容..." }
    ],
    education: [
      {
        degree: "学位名称",
        institution: "大学名称",
        period: "2020-2024",
        logo: "",
        description: "专业描述..."
      }
    ],
    experience: [
      {
        position: "职位名称",
        company: "公司名称",
        period: "2024-至今",
        logo: "",
        description: "工作描述..."
      }
    ],
    publications: [
      {
        title: "论文标题",
        authors: "作者列表",
        venue: "发表会议/期刊",
        url: "",
        description: "论文简介..."
      }
    ]
  };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', loadConfig);
